import React from 'react';

const Footer = () => {
  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wider">
              About
            </h3>
            <p className="mt-4 text-sm text-gray-600">
              A Progressive Web App for form submission with advanced features like file upload, 
              map selection, and OTP verification.
            </p>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wider">
              Features
            </h3>
            <ul className="mt-4 space-y-2">
              <li className="text-sm text-gray-600">✓ Offline Support</li>
              <li className="text-sm text-gray-600">✓ File Upload</li>
              <li className="text-sm text-gray-600">✓ Map Integration</li>
              <li className="text-sm text-gray-600">✓ OTP Verification</li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wider">
              Tech Stack
            </h3>
            <ul className="mt-4 space-y-2">
              <li className="text-sm text-gray-600">React + Vite</li>
              <li className="text-sm text-gray-600">Tailwind CSS</li>
              <li className="text-sm text-gray-600">PWA Ready</li>
              <li className="text-sm text-gray-600">Docker Support</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-center text-sm text-gray-500">
            © 2024 Landing Form PWA. Built with React and Tailwind CSS.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
