{"name": "test-tailwind", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest --coverage", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose up --build", "docker:down": "docker-compose down", "docker:down-dev": "docker-compose -f docker-compose.dev.yml down"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tailwindcss/vite": "^4.1.11", "@types/leaflet": "^1.9.20", "axios": "^1.10.0", "date-fns": "^4.1.0", "leaflet": "^1.9.4", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-multi-date-picker": "^5.0.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.60.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.3", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.11", "vite-plugin-pwa": "^1.0.1", "workbox-webpack-plugin": "^7.3.0", "workbox-window": "^7.3.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.3"}}