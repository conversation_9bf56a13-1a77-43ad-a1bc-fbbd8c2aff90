# Landing Form PWA

A Progressive Web App for form submission with file upload, map selection, and OTP verification.

## Features

- **Progressive Web App (PWA)** - Works offline and can be installed on mobile devices
- **Multi-step Form** - User-friendly form with validation
- **File Upload** - Multiple file uploads with progress tracking
- **Map Integration** - Location selection using OpenStreetMap
- **OTP Verification** - Phone number verification with OTP
- **reCAPTCHA** - Form protection with Google reCAPTCHA
- **Responsive Design** - Works on all devices
- **Docker Support** - Development and production Docker configurations
- **CI/CD Pipeline** - GitHub Actions workflow for testing, building, and deployment

## Tech Stack

- **Frontend**: React, React Router, Tailwind CSS
- **Form Handling**: React Hook Form, Yup validation
- **Map**: Leaflet/OpenStreetMap
- **Notifications**: React Toastify
- **File Upload**: React Dropzone
- **PWA**: Workbox
- **Containerization**: Docker, Docker Compose
- **CI/CD**: GitHub Actions

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm or yarn
- Docker and Docker Compose (optional)

### Installation

1. <PERSON>lone the repository:

   ```bash
   git clone https://github.com/yourusername/landing-form-pwa.git
   cd landing-form-pwa
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Create environment files:

   ```bash
   cp .env.example .env.development
   ```

4. Update the environment variables in `.env.development` with your API keys and configuration.

5. Start the development server:
   ```bash
   npm run dev
   ```

### Docker Development

To run the application using Docker for development:

```bash
npm run docker:dev
```

This will start the frontend, backend, database, and other services defined in `docker-compose.dev.yml`.

### Production Deployment

To build and run the application for production:

```bash
npm run build
npm run preview
```

Or using Docker:

```bash
npm run docker:prod
```

## Project Structure

```
landing-form-pwa/
├── public/                  # Static assets
│   ├── icons/               # PWA icons
│   ├── manifest.json        # PWA manifest
│   ├── sw.js                # Service worker
│   └── offline.html         # Offline page
├── src/
│   ├── assets/              # Images, fonts, etc.
│   ├── components/          # Reusable components
│   │   ├── ui/              # UI components (buttons, inputs, etc.)
│   │   └── form/            # Form-specific components
│   ├── hooks/               # Custom React hooks
│   ├── pages/               # Page components
│   ├── services/            # API services
│   ├── utils/               # Utility functions
│   ├── App.jsx              # Main App component
│   └── main.jsx             # Entry point
├── .env.development         # Development environment variables
├── .env.production          # Production environment variables
├── .env.example             # Example environment variables
├── Dockerfile               # Production Docker configuration
├── Dockerfile.dev           # Development Docker configuration
├── docker-compose.yml       # Production Docker Compose
├── docker-compose.dev.yml   # Development Docker Compose
└── nginx.conf               # Nginx configuration for production
```

## Environment Variables

The application uses the following environment variables:

| Variable                   | Description                    |
| -------------------------- | ------------------------------ |
| `VITE_API_BASE_URL`        | Base URL for API requests      |
| `VITE_RECAPTCHA_SITE_KEY`  | Google reCAPTCHA site key      |
| `VITE_GOOGLE_MAPS_API_KEY` | Google Maps API key (optional) |

See `.env.example` for a complete list of environment variables.

## Docker Configuration

The project includes Docker configurations for both development and production:

- `Dockerfile.dev` - Development configuration with hot-reloading
- `Dockerfile` - Production configuration with Nginx
- `docker-compose.dev.yml` - Development services (frontend, backend, database, etc.)
- `docker-compose.yml` - Production services

## CI/CD Pipeline

The project includes a GitHub Actions workflow for continuous integration and deployment:

- Runs tests on pull requests and pushes to main/develop branches
- Builds and pushes Docker images to GitHub Container Registry
- Deploys to staging environment for develop branch
- Deploys to production environment for main branch

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add my feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
