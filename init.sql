-- Database initialization script for Landing Form PWA

-- Create database (if not exists)
-- CREATE DATABASE landing_form;

-- Connect to the database
-- \c landing_form;

-- Create submissions table
CREATE TABLE IF NOT EXISTS submissions (
    id SERIAL PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    selected_date DATE NOT NULL,
    location JSONB NOT NULL,
    message TEXT,
    files JSONB DEFAULT '[]',
    captcha_verified BOOLEAN DEFAULT FALSE,
    otp_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_submissions_email ON submissions(email);

-- <PERSON>reate index on created_at for sorting
CREATE INDEX IF NOT EXISTS idx_submissions_created_at ON submissions(created_at);

-- Create OTP storage table
CREATE TABLE IF NOT EXISTS otp_codes (
    id SERIAL PRIMARY KEY,
    phone_number VARCHAR(20) NOT NULL,
    otp_code VARCHAR(6) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on phone_number and expires_at
CREATE INDEX IF NOT EXISTS idx_otp_phone_expires ON otp_codes(phone_number, expires_at);

-- Create files table for tracking uploaded files
CREATE TABLE IF NOT EXISTS uploaded_files (
    id SERIAL PRIMARY KEY,
    submission_id INTEGER REFERENCES submissions(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on submission_id
CREATE INDEX IF NOT EXISTS idx_files_submission_id ON uploaded_files(submission_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_submissions_updated_at 
    BEFORE UPDATE ON submissions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data (optional, for testing)
-- INSERT INTO submissions (
--     first_name, 
--     last_name, 
--     email, 
--     phone, 
--     selected_date, 
--     location, 
--     message,
--     captcha_verified,
--     otp_verified
-- ) VALUES (
--     'John', 
--     'Doe', 
--     '<EMAIL>', 
--     '+1234567890', 
--     '2024-01-15', 
--     '{"lat": 51.505, "lng": -0.09, "address": "London, UK"}',
--     'This is a test submission',
--     true,
--     true
-- );

-- Create view for submission statistics
CREATE OR REPLACE VIEW submission_stats AS
SELECT 
    DATE(created_at) as submission_date,
    COUNT(*) as total_submissions,
    COUNT(CASE WHEN otp_verified = true THEN 1 END) as verified_submissions,
    COUNT(CASE WHEN files != '[]' THEN 1 END) as submissions_with_files
FROM submissions 
GROUP BY DATE(created_at)
ORDER BY submission_date DESC;
