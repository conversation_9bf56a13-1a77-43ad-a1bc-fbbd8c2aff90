import loginImg from "assets/images/logo.png";

const Header = () => {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200 h-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-center items-center py-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <img src={loginImg} alt="login" className="w-auto h-[15px]" />
              {/* <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
              )} */}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* PWA Install Button */}
            <button
              id="install-button"
              className="hidden bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              نصب برنامه
            </button>

            {/* Connection Status */}
            {/* <div id="connection-status" className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm text-gray-600">آنلاین</span>
            </div> */}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
