@import "theme";
@import "tailwindcss";

@layer base {
  input[type="checkbox"] {
    accent-color: var(--light-primary-background-rest);
    outline: none;
  }

  svg {
    font-family: var(--body-medium-font-family), serif !important;
  }

  .responsive-svg svg {
    width: 100% !important;
  }

  .datepicker-container {
    width: 100%;
  }

  .datepicker-container .rmdp-input {
    border: none;
    background-color: transparent;
    width: 100%;
  }
  .datepicker-container .rmdp-input:focus {
    box-shadow: none;
    border: none;
  }

  /* Bold Large */
  .font-body-bold-large {
    font-family: var(--body-bold-large-font-family), serif;
    font-size: var(--body-bold-large-font-size);
    font-style: var(--body-bold-large-font-style);
    font-weight: var(--body-bold-large-font-weight);
    letter-spacing: var(--body-bold-large-letter-spacing);
    line-height: var(--body-bold-large-line-height);
  }

  /* Bold Medium */
  .font-body-bold-medium {
    font-family: var(--body-bold-medium-font-family), serif;
    font-size: var(--body-bold-medium-font-size);
    font-style: var(--body-bold-medium-font-style);
    font-weight: var(--body-bold-medium-font-weight);
    letter-spacing: var(--body-bold-medium-letter-spacing);
    line-height: var(--body-bold-medium-line-height);
  }

  /* Bold Small */
  .font-body-bold-small {
    font-family: var(--body-bold-small-font-family), serif;
    font-size: var(--body-bold-small-font-size);
    font-style: var(--body-bold-small-font-style);
    font-weight: var(--body-bold-small-font-weight);
    letter-spacing: var(--body-bold-small-letter-spacing);
    line-height: var(--body-bold-small-line-height);
  }

  /* Large */
  .font-body-large {
    font-family: var(--body-large-font-family), serif;
    font-size: var(--body-large-font-size);
    font-style: var(--body-large-font-style);
    font-weight: var(--body-large-font-weight);
    letter-spacing: var(--body-large-letter-spacing);
    line-height: var(--body-large-line-height);
  }

  /* Medium */
  .font-body-medium {
    font-family: var(--body-medium-font-family), serif;
    font-size: var(--body-medium-font-size);
    font-style: var(--body-medium-font-style);
    font-weight: var(--body-medium-font-weight);
    letter-spacing: var(--body-medium-letter-spacing);
    line-height: var(--body-medium-line-height);
  }

  /* Small */
  .font-body-small {
    font-family: var(--body-small-font-family), serif;
    font-size: var(--body-small-font-size);
    font-style: var(--body-small-font-style);
    font-weight: var(--body-small-font-weight);
    letter-spacing: var(--body-small-letter-spacing);
    line-height: var(--body-small-line-height);
  }

  /* Button Large */
  .font-button-large {
    font-family: var(--button-large-font-family), serif;
    font-size: var(--button-large-font-size);
    font-style: var(--button-large-font-style);
    font-weight: var(--button-large-font-weight);
    letter-spacing: var(--button-large-letter-spacing);
    line-height: var(--button-large-line-height);
  }

  /* Button Medium */
  .font-button-medium {
    font-family: var(--button-medium-font-family), serif;
    font-size: var(--button-medium-font-size);
    font-style: var(--button-medium-font-style);
    font-weight: var(--button-medium-font-weight);
    letter-spacing: var(--button-medium-letter-spacing);
    line-height: var(--button-medium-line-height);
  }

  /* Button Small */
  .font-button-small {
    font-family: var(--button-small-font-family), serif;
    font-size: var(--button-small-font-size);
    font-style: var(--button-small-font-style);
    font-weight: var(--button-small-font-weight);
    letter-spacing: var(--button-small-letter-spacing);
    line-height: var(--button-small-line-height);
  }

  /* Headline Large */
  .font-headline-large {
    font-family: var(--headline-large-font-family), serif;
    font-size: var(--headline-large-font-size);
    font-style: var(--headline-large-font-style);
    font-weight: var(--headline-large-font-weight);
    letter-spacing: var(--headline-large-letter-spacing);
    line-height: var(--headline-large-line-height);
  }

  /* Headline Medium */
  .font-headline-medium {
    font-family: var(--headline-medium-font-family), serif;
    font-size: var(--headline-medium-font-size);
    font-style: var(--headline-medium-font-style);
    font-weight: var(--headline-medium-font-weight);
    letter-spacing: var(--headline-medium-letter-spacing);
    line-height: var(--headline-medium-line-height);
  }

  /* Headline Small */
  .font-headline-small {
    font-family: var(--headline-small-font-family), serif;
    font-size: var(--headline-small-font-size);
    font-style: var(--headline-small-font-style);
    font-weight: var(--headline-small-font-weight);
    letter-spacing: var(--headline-small-letter-spacing);
    line-height: var(--headline-small-line-height);
  }

  /* Overline Large */
  .font-overline-large {
    font-family: var(--overline-large-font-family), serif;
    font-size: var(--overline-large-font-size);
    font-style: var(--overline-large-font-style);
    font-weight: var(--overline-large-font-weight);
    letter-spacing: var(--overline-large-letter-spacing);
    line-height: var(--overline-large-line-height);
  }

  /* Overline Medium */
  .font-overline-medium {
    font-family: var(--overline-medium-font-family), serif;
    font-size: var(--overline-medium-font-size);
    font-style: var(--overline-medium-font-style);
    font-weight: var(--overline-medium-font-weight);
    letter-spacing: var(--overline-medium-letter-spacing);
    line-height: var(--overline-medium-line-height);
  }

  /* Overline Small */
  .font-overline-small {
    font-family: var(--overline-small-font-family), serif;
    font-size: var(--overline-small-font-size);
    font-style: var(--overline-small-font-style);
    font-weight: var(--overline-small-font-weight);
    letter-spacing: var(--overline-small-letter-spacing);
    line-height: var(--overline-small-line-height);
  }

  /* Paragraph Bold Large */
  .font-paragraph-bold-large {
    font-family: var(--paragraph-bold-large-font-family), serif;
    font-size: var(--paragraph-bold-large-font-size);
    font-style: var(--paragraph-bold-large-font-style);
    font-weight: var(--paragraph-bold-large-font-weight);
    letter-spacing: var(--paragraph-bold-large-letter-spacing);
    line-height: var(--paragraph-bold-large-line-height);
  }

  /* Paragraph Bold Medium */
  .font-paragraph-bold-medium {
    font-family: var(--paragraph-bold-medium-font-family), serif;
    font-size: var(--paragraph-bold-medium-font-size);
    font-style: var(--paragraph-bold-medium-font-style);
    font-weight: var(--paragraph-bold-medium-font-weight);
    letter-spacing: var(--paragraph-bold-medium-letter-spacing);
    line-height: var(--paragraph-bold-medium-line-height);
  }

  /* Paragraph Bold Small */
  .font-paragraph-bold-small {
    font-family: var(--paragraph-bold-small-font-family), serif;
    font-size: var(--paragraph-bold-small-font-size);
    font-style: var(--paragraph-bold-small-font-style);
    font-weight: var(--paragraph-bold-small-font-weight);
    letter-spacing: var(--paragraph-bold-small-letter-spacing);
    line-height: var(--paragraph-bold-small-line-height);
  }

  /* Paragraph Large */
  .font-paragraph-large {
    font-family: var(--paragraph-large-font-family), serif;
    font-size: var(--paragraph-large-font-size);
    font-style: var(--paragraph-large-font-style);
    font-weight: var(--paragraph-large-font-weight);
    letter-spacing: var(--paragraph-large-letter-spacing);
    line-height: var(--paragraph-large-line-height);
  }

  /* Paragraph Medium */
  .font-paragraph-medium {
    font-family: var(--paragraph-medium-font-family), serif;
    font-size: var(--paragraph-medium-font-size);
    font-style: var(--paragraph-medium-font-style);
    font-weight: var(--paragraph-medium-font-weight);
    letter-spacing: var(--paragraph-medium-letter-spacing);
    line-height: var(--paragraph-medium-line-height);
  }

  /* Paragraph Small */
  .font-paragraph-small {
    font-family: var(--paragraph-small-font-family), serif;
    font-size: var(--paragraph-small-font-size);
    font-style: var(--paragraph-small-font-style);
    font-weight: var(--paragraph-small-font-weight);
    letter-spacing: var(--paragraph-small-letter-spacing);
    line-height: var(--paragraph-small-line-height);
  }

  /* Subtitle Large */
  .font-subtitle-large {
    font-family: var(--subtitle-large-font-family), serif;
    font-size: var(--subtitle-large-font-size);
    font-style: var(--subtitle-large-font-style);
    font-weight: var(--subtitle-large-font-weight);
    letter-spacing: var(--subtitle-large-letter-spacing);
    line-height: var(--subtitle-large-line-height);
  }

  /* Subtitle Medium */
  .font-subtitle-medium {
    font-family: var(--subtitle-medium-font-family), serif;
    font-size: var(--subtitle-medium-font-size);
    font-style: var(--subtitle-medium-font-style);
    font-weight: var(--subtitle-medium-font-weight);
    letter-spacing: var(--subtitle-medium-letter-spacing);
    line-height: var(--subtitle-medium-line-height);
  }

  /* Subtitle Small */
  .font-subtitle-small {
    font-family: var(--subtitle-small-font-family), serif;
    font-size: var(--subtitle-small-font-size);
    font-style: var(--subtitle-small-font-style);
    font-weight: var(--subtitle-small-font-weight);
    letter-spacing: var(--subtitle-small-letter-spacing);
    line-height: var(--subtitle-small-line-height);
  }

  /* Title Large */
  .font-title-large {
    font-family: var(--title-large-font-family), serif;
    font-size: var(--title-large-font-size);
    font-style: var(--title-large-font-style);
    font-weight: var(--title-large-font-weight);
    letter-spacing: var(--title-large-letter-spacing);
    line-height: var(--title-large-line-height);
  }

  /* Title Medium */
  .font-title-medium {
    font-family: var(--title-medium-font-family), serif;
    font-size: var(--title-medium-font-size);
    font-style: var(--title-medium-font-style);
    font-weight: var(--title-medium-font-weight);
    letter-spacing: var(--title-medium-letter-spacing);
    line-height: var(--title-medium-line-height);
  }

  /* Title Small */
  .font-title-small {
    font-family: var(--title-small-font-family), serif;
    font-size: var(--title-small-font-size);
    font-style: var(--title-small-font-style);
    font-weight: var(--title-small-font-weight);
    letter-spacing: var(--title-small-letter-spacing);
    line-height: var(--title-small-line-height);
  }
}
